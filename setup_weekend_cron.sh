#!/bin/bash

# 设置周末广播定时任务脚本
# 每周六周日每2小时35分执行一次

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEEKEND_SCRIPT="$SCRIPT_DIR/weekend_broadcast.py"
PYTHON_PATH=$(which python3 || which python)

echo "🚀 正在设置周末广播定时任务..."

# 检查weekend_broadcast.py是否存在
if [[ ! -f "$WEEKEND_SCRIPT" ]]; then
    echo "❌ 错误: 找不到 weekend_broadcast.py 脚本"
    exit 1
fi

# 检查Python是否可用
if [[ -z "$PYTHON_PATH" ]]; then
    echo "❌ 错误: 找不到Python解释器"
    exit 1
fi

echo "✅ Python路径: $PYTHON_PATH"
echo "✅ 脚本路径: $WEEKEND_SCRIPT"

# 备份当前的crontab
echo "📋 备份当前crontab..."
BACKUP_FILE="/tmp/crontab_backup_weekend_$(date +%Y%m%d_%H%M%S)"
crontab -l > "$BACKUP_FILE" 2>/dev/null || echo "当前没有crontab任务"
echo "✅ 备份文件: $BACKUP_FILE"

# 检查是否已经存在相同的任务
if crontab -l 2>/dev/null | grep -q "weekend_broadcast.py"; then
    echo "⚠️  警告: 已存在周末广播任务，将替换现有任务"
    # 移除现有的周末广播任务（只移除weekend_broadcast相关的）
    crontab -l 2>/dev/null | grep -v "weekend_broadcast.py" | grep -v "# 周末广播定时任务" | grep -v "# 执行时间:" > /tmp/temp_cron_clean
    crontab /tmp/temp_cron_clean
    rm -f /tmp/temp_cron_clean
fi

# 添加新的定时任务
echo "⏰ 添加周末广播定时任务..."

# 创建临时crontab文件
TEMP_CRON=$(mktemp)

# 获取现有的crontab内容（保留所有现有任务）
crontab -l 2>/dev/null > "$TEMP_CRON" || true

# 计算每2小时35分的时间点
# 2小时35分 = 155分钟
# 一天24小时 = 1440分钟
# 1440 ÷ 155 ≈ 9.29，所以一天大约可以执行9次

# 周末广播时间安排（每2小时35分执行一次）：
# 00:00, 02:35, 05:10, 07:45, 10:20, 12:55, 15:30, 18:05, 20:40, 23:15

echo "# 周末广播定时任务 - 每周六周日每2小时35分执行一次" >> "$TEMP_CRON"
echo "# 执行时间: 00:00, 02:35, 05:10, 07:45, 10:20, 12:55, 15:30, 18:05, 20:40, 23:15" >> "$TEMP_CRON"

# 添加周六的任务
echo "0 0 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "35 2 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "10 5 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "45 7 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "20 10 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "55 12 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "30 15 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "5 18 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "40 20 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "15 23 * * 6 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"

# 添加周日的任务
echo "0 0 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "35 2 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "10 5 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "45 7 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "20 10 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "55 12 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "30 15 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "5 18 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "40 20 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"
echo "15 23 * * 0 cd $SCRIPT_DIR && $PYTHON_PATH weekend_broadcast.py >> $SCRIPT_DIR/weekend_cron.log 2>&1" >> "$TEMP_CRON"

# 安装新的crontab
crontab "$TEMP_CRON"

# 清理临时文件
rm -f "$TEMP_CRON"

echo ""
echo "🎉 周末广播定时任务设置完成！"
echo ""
echo "📅 任务详情:"
echo "   • 执行日期: 每周六、周日"
echo "   • 执行频率: 每2小时35分钟一次"
echo "   • 每日执行次数: 10次"
echo "   • 日志文件: $SCRIPT_DIR/weekend_cron.log"
echo ""
echo "⏰ 执行时间表:"
echo "   00:00  02:35  05:10  07:45  10:20"
echo "   12:55  15:30  18:05  20:40  23:15"
echo ""
echo "📋 当前所有crontab任务:"
crontab -l
echo ""
echo "🔧 管理命令:"
echo "   查看任务: crontab -l"
echo "   编辑任务: crontab -e"
echo "   查看日志: tail -f $SCRIPT_DIR/weekend_cron.log"
echo "   恢复备份: crontab $BACKUP_FILE"
echo ""
echo "🧪 测试命令:"
echo "   手动执行: cd '$SCRIPT_DIR' && $PYTHON_PATH weekend_broadcast.py"
