# 日志清理脚本使用说明

## 概述
这个脚本套件用于自动清理 Tree Sg Bot 目录下的日志文件，包括：
- `broadcast_*.log` - 广播日志文件
- `forward_channel_*.log` - 转发频道日志文件  
- `logs/` 目录下的所有 `.log` 文件

## 文件说明

### 1. cleanup_logs.sh
主要的清理脚本，负责删除超过指定天数的日志文件。

**使用方法:**
```bash
# 使用默认保留天数（7天）
./cleanup_logs.sh

# 指定保留天数（例如保留3天）
./cleanup_logs.sh 3
```

### 2. setup_cron.sh
用于设置定时任务的脚本，会自动配置crontab。

**使用方法:**
```bash
./setup_cron.sh
```

## 快速开始

### 1. 设置定时任务
```bash
cd "/python/Tree Sg Bot"
./setup_cron.sh
```

这将设置每天凌晨2点自动清理超过7天的日志文件。

### 2. 手动清理
```bash
# 清理超过7天的日志
./cleanup_logs.sh

# 清理超过3天的日志
./cleanup_logs.sh 3
```

### 3. 查看清理日志
```bash
# 查看清理历史
cat cleanup.log

# 查看cron执行日志
cat cron.log
```

## 定时任务管理

### 查看当前定时任务
```bash
crontab -l
```

### 编辑定时任务
```bash
crontab -e
```

### 删除定时任务
```bash
crontab -r
```

## 自定义配置

### 修改执行时间
编辑crontab，修改时间设置：
```bash
crontab -e
```

常用时间格式：
- `0 2 * * *` - 每天凌晨2点
- `0 */6 * * *` - 每6小时执行一次
- `0 2 * * 0` - 每周日凌晨2点

### 修改保留天数
在crontab中修改命令参数：
```bash
# 保留3天的日志
0 2 * * * /path/to/cleanup_logs.sh 3 >> /path/to/cron.log 2>&1
```

## 安全特性

1. **备份**: setup_cron.sh 会自动备份现有的crontab
2. **日志记录**: 所有清理操作都会记录到 cleanup.log
3. **错误处理**: 脚本包含错误检查和处理机制
4. **自清理**: cleanup.log 文件本身也会定期清理（保留30天）

## 故障排除

### 权限问题
```bash
chmod +x cleanup_logs.sh
chmod +x setup_cron.sh
```

### 查看cron服务状态
```bash
# Ubuntu/Debian
sudo systemctl status cron

# CentOS/RHEL
sudo systemctl status crond
```

### 手动测试
```bash
# 测试清理脚本（不实际删除，只显示会删除的文件）
find . -name "*.log" -mtime +7 -type f
```

## 注意事项

1. 首次运行前请确保重要日志已备份
2. 建议先用较小的保留天数测试脚本功能
3. 定期检查 cleanup.log 确认清理正常执行
4. 如果磁盘空间紧张，可以适当减少保留天数
