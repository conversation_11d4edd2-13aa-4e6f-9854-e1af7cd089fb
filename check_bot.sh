#!/bin/bash

# 目标目录
BOT_DIR="/python/Tree Sg Bot"
BOT_NAME="main.py"

# 日志目录
LOG_DIR="$BOT_DIR/logs"
mkdir -p "$LOG_DIR"

# 日志文件
DATE=$(date "+%Y-%m-%d")
CHECK_LOG="$LOG_DIR/check_$DATE.log"
BOT_LOG="$LOG_DIR/bot_$DATE.log"
ERROR_LOG="$LOG_DIR/error_$DATE.log"

# 日志轮转（保留最近7天的日志）
find "$LOG_DIR" -name "*.log" -mtime +7 -delete

cd "$BOT_DIR" || exit

# 获取当前时间
NOW=$(date "+%Y-%m-%d %H:%M:%S")

# 检查进程健康状态的函数
check_process_health() {
    local pid=$1
    if [[ -z "$pid" ]]; then
        return 1  # 没有PID，进程不存在
    fi

    # 检查进程是否存在
    if ! ps -p "$pid" > /dev/null 2>&1; then
        return 1  # 进程不存在
    fi

    # 检查进程是否响应（可以添加更多健康检查逻辑）
    # 这里可以根据具体需求添加更多检查，比如检查日志文件更新时间等
    return 0  # 进程健康
}

# 重启进程的函数
restart_bot() {
    echo "[$NOW] INFO: Restarting bot process..." >> "$CHECK_LOG"

    # 强制终止所有相关的Python进程
    echo "[$NOW] INFO: Killing all existing bot processes..." >> "$CHECK_LOG"
    pkill -f "python3.9 $BOT_DIR/$BOT_NAME"
    sleep 2  # 等待进程完全终止

    # 启动机器人
    echo "[$NOW] INFO: Starting $BOT_NAME..." >> "$CHECK_LOG"
    echo "[$NOW] INFO: Starting bot process..." >> "$BOT_LOG"

    # 启动机器人并将输出重定向到日志文件
    nohup python3.9 "$BOT_DIR/$BOT_NAME" >> "$BOT_LOG" 2>> "$ERROR_LOG" &

    # 记录进程ID
    BOT_PID=$!
    echo "[$NOW] INFO: Bot started with PID: $BOT_PID" >> "$CHECK_LOG"

    # 等待几秒检查进程是否成功启动
    sleep 5
    if ps -p $BOT_PID > /dev/null; then
        echo "[$NOW] INFO: Bot successfully started and running" >> "$CHECK_LOG"
    else
        echo "[$NOW] ERROR: Bot failed to start properly" >> "$CHECK_LOG"
        echo "[$NOW] ERROR: Check error log for details" >> "$CHECK_LOG"
    fi
}

# 主逻辑：检查进程状态
BOT_PID=$(pgrep -f "python3.9 $BOT_DIR/$BOT_NAME")

if [[ -z "$BOT_PID" ]]; then
    # 没有找到进程，需要启动
    echo "[$NOW] INFO: Bot not running. Starting $BOT_NAME..." >> "$CHECK_LOG"
    restart_bot
else
    # 找到进程，检查健康状态
    echo "[$NOW] INFO: Found bot process with PID: $BOT_PID" >> "$CHECK_LOG"

    if check_process_health "$BOT_PID"; then
        # 进程健康，无需重启
        echo "[$NOW] INFO: Bot process is healthy and running normally. No action needed." >> "$CHECK_LOG"
    else
        # 进程不健康，需要重启
        echo "[$NOW] WARNING: Bot process is unhealthy. Restarting..." >> "$CHECK_LOG"
        restart_bot
    fi
fi

# 记录系统资源使用情况
echo "[$NOW] INFO: System resource usage:" >> "$CHECK_LOG"
echo "Memory usage:" >> "$CHECK_LOG"
free -h | grep -v + >> "$CHECK_LOG"
echo "Disk usage:" >> "$CHECK_LOG"
df -h "$BOT_DIR" >> "$CHECK_LOG"
