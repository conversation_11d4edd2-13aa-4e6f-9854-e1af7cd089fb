import json
import asyncio
import logging
import datetime
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.error import TelegramError
from telegram.constants import ParseMode
from telegram.request import HTTPXRequest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'weekend_broadcast_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
BOT_TOKEN = "8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI"  # 替换为你的bot token
DB_FILE = "users.json"
DEMO_IMAGE_PATH = "demo.png"  # 当前目录的demo.png图片

# 周末活动广播内容 - 具有引导性和刺激性的文案
WEEKEND_BROADCAST_CONTENT = """
🔥<b>【限时狂欢】周末专享超值活动来袭！</b>🔥

💥 <b>震撼价格！仅需8.8元！</b> 💥
🎯 <b>买一送一！15天会员到手！</b> 🎯

⏰ <b>活动时间：</b>仅限周六、周日
💰 <b>原价周卡：</b>~~15元~~
🎁 <b>周末特价：</b><u>8.8元买一送一</u>
📅 <b>实际获得：</b>15天超值会员

⚡ <b>机会稍纵即逝！</b>
🔥 仅2天特惠时间
💎 数量有限，先到先得

<i>💡 温馨提示：此优惠仅在周六周日有效，随时恢复原价！</i>

👇 <b>立即抢购，开启极致体验！</b> 👇
"""

async def load_users():
    """加载用户数据"""
    try:
        with open(DB_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            users = [int(user_id) for user_id in data.get("users", {}).keys()]
            logger.info(f"成功加载 {len(users)} 个用户")
            return users
    except Exception as e:
        logger.error(f"加载用户数据失败: {str(e)}")
        return []

async def send_weekend_message(bot, user_id):
    """发送周末活动消息给单个用户（包含图片和按钮）"""
    try:
        # 创建内联键盘 - 立即体验按钮
        keyboard = [
            [
                InlineKeyboardButton("🚀 立即体验", url="https://idatas.qnm6.top")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # 发送图片和文字消息
        with open(DEMO_IMAGE_PATH, 'rb') as photo:
            await bot.send_photo(
                chat_id=user_id,
                photo=photo,
                caption=WEEKEND_BROADCAST_CONTENT,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )
        
        logger.info(f"成功发送周末活动消息给用户 {user_id}")
        return True
    except TelegramError as e:
        logger.error(f"发送消息给用户 {user_id} 失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"发送消息给用户 {user_id} 时发生未知错误: {str(e)}")
        return False

def is_weekend():
    """检查当前是否为周末（周六或周日）"""
    today = datetime.datetime.now().weekday()
    return today in [5, 6]  # 5=周六, 6=周日

async def weekend_broadcast():
    """周末活动广播"""
    logger.info("开始周末活动广播任务")
    
    # 检查是否为周末
    if not is_weekend():
        current_day = datetime.datetime.now().strftime("%A")
        logger.warning(f"今天是 {current_day}，不是周末，广播终止")
        print(f"⚠️  今天不是周末，周末活动广播已取消")
        return
    
    logger.info("✅ 确认今天是周末，开始广播周末活动")
    logger.info(f"广播内容: {WEEKEND_BROADCAST_CONTENT}")
    
    # 加载用户
    users = await load_users()
    if not users:
        logger.error("没有找到用户，广播终止")
        return
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,    # 减小连接池大小
        pool_timeout=60.0,         # 增加连接池超时时间
        read_timeout=60.0,         # 增加读取超时时间
        write_timeout=60.0         # 增加写入超时时间
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 统计变量
    total_users = len(users)
    success_count = 0
    fail_count = 0
    
    logger.info(f"开始发送周末活动广播，总用户数: {total_users}")
    
    # 分批发送
    batch_size = 6  # 由于要发送图片，减小批次大小
    delay = 3  # 增加批次间延迟，避免被限制
    
    for i in range(0, total_users, batch_size):
        batch = users[i:i + batch_size]
        logger.info(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch)} 个用户")
        
        # 创建发送任务
        tasks = [send_weekend_message(bot, user_id) for user_id in batch]
        
        # 等待当前批次完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新统计
        batch_success = sum(1 for r in results if r is True)
        batch_fail = sum(1 for r in results if r is not True)
        success_count += batch_success
        fail_count += batch_fail
        
        logger.info(f"批次完成 - 成功: {batch_success}, 失败: {batch_fail}")
        
        # 批次间延迟
        if i + batch_size < total_users:
            logger.info(f"等待 {delay} 秒后发送下一批...")
            await asyncio.sleep(delay)
    
    # 打印最终统计
    logger.info("周末活动广播任务完成")
    logger.info(f"""
🎉 周末活动广播统计:
• 总用户数: {total_users}
• 成功: {success_count}
• 失败: {fail_count}
• 成功率: {(success_count/total_users*100):.1f}%
""")

async def test_weekend_broadcast():
    """测试周末活动广播给特定用户"""
    logger.info("开始测试周末活动广播")
    test_user_id = 7346442935
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,
        pool_timeout=60.0,
        read_timeout=60.0,
        write_timeout=60.0
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 发送测试消息
    success = await send_weekend_message(bot, test_user_id)
    
    if success:
        logger.info(f"测试周末活动广播成功发送给用户 {test_user_id}")
    else:
        logger.error(f"测试周末活动广播发送给用户 {test_user_id} 失败")

if __name__ == "__main__":
    try:
        # 运行周末活动广播
        asyncio.run(weekend_broadcast())
        
        # 如果需要测试，取消下面的注释
        # asyncio.run(test_weekend_broadcast())
    except KeyboardInterrupt:
        logger.info("周末活动广播任务被用户中断")
    except Exception as e:
        logger.error(f"周末活动广播任务发生错误: {str(e)}")
