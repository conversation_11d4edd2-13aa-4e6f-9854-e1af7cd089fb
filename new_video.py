import json
import asyncio
import logging
import datetime
from telegram import Bo<PERSON>
from telegram.error import TelegramError
from telegram.request import HTTPXRequest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'forward_channel_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置
BOT_TOKEN = "8095944049:AAFJajM4iwirm-DdQa70hP7lKw3-kFn2JXI"  # 替换为你的bot token
DB_FILE = "users.json"
CHANNEL_MESSAGE_ID = 346  # 频道消息ID
CHANNEL_USERNAME = "idatas8"  # 频道用户名

async def load_users():
    """加载用户数据"""
    try:
        with open(DB_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            users = [int(user_id) for user_id in data.get("users", {}).keys()]
            logger.info(f"成功加载 {len(users)} 个用户")
            return users
    except Exception as e:
        logger.error(f"加载用户数据失败: {str(e)}")
        return []

async def forward_message(bot, user_id):
    """转发频道消息给单个用户"""
    try:
        # 转发频道消息
        await bot.forward_message(
            chat_id=user_id,
            from_chat_id=f"@{CHANNEL_USERNAME}",
            message_id=CHANNEL_MESSAGE_ID
        )
        logger.info(f"成功转发频道消息给用户 {user_id}")
        return True
    except TelegramError as e:
        logger.error(f"转发频道消息给用户 {user_id} 失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"转发频道消息给用户 {user_id} 时发生未知错误: {str(e)}")
        return False

async def broadcast_forward():
    """广播转发频道消息给所有用户"""
    logger.info("开始转发频道消息任务")
    logger.info(f"转发频道: @{CHANNEL_USERNAME}, 消息ID: {CHANNEL_MESSAGE_ID}")
    
    # 加载用户
    users = await load_users()
    if not users:
        logger.error("没有找到用户，转发终止")
        return
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,
        pool_timeout=60.0,
        read_timeout=60.0,
        write_timeout=60.0
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 统计变量
    total_users = len(users)
    success_count = 0
    fail_count = 0
    
    logger.info(f"开始转发频道消息，总用户数: {total_users}")
    
    # 分批转发
    batch_size = 5
    delay = 3
    
    for i in range(0, total_users, batch_size):
        batch = users[i:i + batch_size]
        logger.info(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch)} 个用户")
        
        # 创建转发任务
        tasks = [forward_message(bot, user_id) for user_id in batch]
        
        # 等待当前批次完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新统计
        batch_success = sum(1 for r in results if r is True)
        batch_fail = sum(1 for r in results if r is not True)
        success_count += batch_success
        fail_count += batch_fail
        
        logger.info(f"批次完成 - 成功: {batch_success}, 失败: {batch_fail}")
        
        # 批次间延迟
        if i + batch_size < total_users:
            logger.info(f"等待 {delay} 秒后转发下一批...")
            await asyncio.sleep(delay)
    
    # 打印最终统计
    logger.info("转发频道消息任务完成")
    logger.info(f"""
📊 转发统计:
• 总用户数: {total_users}
• 成功: {success_count}
• 失败: {fail_count}
• 成功率: {(success_count/total_users*100):.1f}%
""")

async def test_forward():
    """测试转发给特定用户"""
    logger.info("开始测试转发")
    test_user_id = 7346442935
    
    # 创建bot实例，配置连接池
    request = HTTPXRequest(
        connection_pool_size=5,
        pool_timeout=60.0,
        read_timeout=60.0,
        write_timeout=60.0
    )
    bot = Bot(token=BOT_TOKEN, request=request)
    
    # 发送测试转发
    success = await forward_message(bot, test_user_id)
    
    if success:
        logger.info(f"测试转发成功发送给用户 {test_user_id}")
    else:
        logger.error(f"测试转发发送给用户 {test_user_id} 失败")

if __name__ == "__main__":
    try:
        asyncio.run(broadcast_forward())
        # asyncio.run(test_forward())
    except KeyboardInterrupt:
        logger.info("转发任务被用户中断")
    except Exception as e:
        logger.error(f"转发任务发生错误: {str(e)}") 