# 周末活动广播脚本使用说明

## 📋 功能概述

这个脚本专门用于周末（周六、周日）的特惠活动广播，具有以下特点：

- 🎯 **智能时间检测**：自动检测是否为周末，非周末时间不会发送
- 🖼️ **图片+文字**：发送 demo.png 图片配合精美文案
- 🔗 **内联按钮**：包含"立即体验"按钮，点击跳转到指定网站
- 📝 **引导性文案**：使用HTML格式和emoji，具有强烈的营销引导性

## 🎨 广播内容特色

### 文案亮点：
- ✨ 使用大量emoji增加视觉冲击力
- 🔥 强调"限时"、"狂欢"等紧迫感词汇
- 💰 突出价格优势：8.8元买一送一
- ⏰ 明确活动时间：仅限周六周日
- 🎁 强调超值：15天会员到手

### 格式美化：
- 使用HTML标签：`<b>`粗体、`<u>`下划线、`<i>`斜体
- 删除线效果：~~原价15元~~
- 分层次展示信息，易于阅读

## 🚀 使用方法

### 1. 直接运行（推荐）
```bash
cd "Tree Sg Bot"
python weekend_broadcast.py
```

### 2. 测试模式
如需测试，编辑脚本最后几行：
```python
# 注释掉正式广播
# asyncio.run(weekend_broadcast())

# 启用测试广播
asyncio.run(test_weekend_broadcast())
```

## ⚙️ 配置说明

### 必要文件：
- `users.json` - 用户数据库文件
- `demo.png` - 活动宣传图片
- Bot Token - 已配置在脚本中

### 可调整参数：
- `batch_size = 3` - 每批发送用户数（图片发送建议较小批次）
- `delay = 5` - 批次间延迟秒数
- `test_user_id = 7346442935` - 测试用户ID

## 📊 功能特性

### 智能时间控制：
- 自动检测当前是否为周末
- 非周末时间运行会自动终止并提示

### 安全发送机制：
- 分批发送，避免API限制
- 异常处理，记录失败原因
- 详细日志记录

### 用户体验优化：
- 图片+文字组合，视觉效果佳
- 内联按钮直接跳转，操作便捷
- HTML格式文案，排版美观

## 📝 日志记录

脚本会自动生成日志文件：
- 文件名格式：`weekend_broadcast_YYYYMMDD_HHMMSS.log`
- 记录发送成功/失败统计
- 包含详细的错误信息

## 🎯 营销效果

### 文案策略：
1. **紧迫感营造**：限时、稍纵即逝、错过再等一周
2. **价值突出**：买一送一、超值划算、震撼价格
3. **行动引导**：立即抢购、开启极致体验
4. **信任建立**：专业服务、品质保证

### 视觉冲击：
- 丰富的emoji表情
- 清晰的价格对比
- 醒目的按钮设计

## ⚠️ 注意事项

1. **时间限制**：脚本只在周六、周日执行广播
2. **图片依赖**：确保 demo.png 文件存在于当前目录
3. **用户数据**：需要有效的 users.json 文件
4. **API限制**：已设置合理的发送频率，避免被限制

## 🔧 故障排除

### 常见问题：
1. **图片发送失败**：检查 demo.png 文件是否存在
2. **用户加载失败**：检查 users.json 文件格式
3. **非周末运行**：脚本会自动检测并提示

### 调试建议：
- 先使用测试模式验证功能
- 查看日志文件了解详细错误
- 确认Bot Token有效性

## 📈 效果预期

通过这个精心设计的广播脚本，预期能够：
- 🎯 提高周末活动参与度
- 💰 促进周卡销售转化
- 📱 增强用户互动体验
- 🚀 提升品牌影响力
